/* ===== MAIN PAGE SECTIONS ===== */

/* ===== HERO SECTION ===== */
.top {
    /* background-image: url("../images/bg-top2.jpeg"); */
    height: 90vh;
    width: 100%;
    display: flex;
    overflow: hidden;
}

.top-title {
    font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--blue-600);
    max-width: 1056px;
    margin-bottom: var(--space-2);
    animation: slideInLeft 1s ease-out;
}

.top-subtitle {
    font-size: clamp(var(--text-xl), 3vw, var(--text-3xl));
    font-weight: var(--font-medium);
    color: var(--blue-600);
    margin-bottom: var(--space-4);
    max-width: 950px;
    animation: slideInLeft 1s ease-out 0.2s both;
}

.top-text {
    font-size: clamp(var(--text-lg), 2vw, var(--text-2xl));
    font-weight: var(--font-light);
    color: var(--blue-600);
    margin-bottom: var(--space-10);
    max-width: 950px;
    line-height: var(--leading-relaxed);
    animation: slideInLeft 1s ease-out 0.4s both;
}

.top-btn {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    padding: var(--space-4) var(--space-8);
    max-width: 283px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--white);
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    border-radius: var(--radius-lg);
    transition: var(--transition-base);
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    animation: slideInLeft 1s ease-out 0.6s both;
    position: relative;
    overflow: hidden;
}

.top-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.top-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    color: var(--white);
}

.top-btn:hover::before {
    left: 0;
}

/* ===== SERVICES SECTION ===== */
.service {
    margin-bottom: var(--space-24);
    padding: var(--space-16) 0;
}

.service h2 {
    position: relative;
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--gray-900);
    text-align: center;
    margin: 0 auto var(--space-16);
    padding: var(--space-4) 0;
    max-width: 621px;
}

.service h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-12);
    max-width: 1342px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.service-item {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-base);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.service-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-item:hover::before {
    transform: scaleX(1);
}

.service-item__pic {
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: center;
}

.service-item__pic img {
    max-width: 120px;
    width: 100%;
    height: auto;
    transition: var(--transition-base);
}

.service-item:hover .service-item__pic img {
    transform: scale(1.1);
}

.service-item__title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--primary-blue);
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.service-item__text {
    font-size: var(--text-base);
    font-weight: var(--font-regular);
    color: var(--gray-700);
    line-height: var(--leading-relaxed);
}

/* ===== MIDDLE CTA SECTION ===== */
.middle {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--blue-700) 100%);
    min-height: 355px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    margin: var(--space-24) 0;
    overflow: hidden;
}

.middle::after {
    content: "";
    position: absolute;
    clip-path: polygon(82% 0, 100% 0, 100% 100%, 49% 100%);
    background: linear-gradient(135deg, rgba(123, 139, 167, 0.1), rgba(123, 139, 167, 0.05));
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.middle div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
    padding: var(--space-8);
}

.middle div p {
    text-align: center;
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-xl), 3vw, var(--text-3xl));
    color: var(--white);
    margin-bottom: var(--space-6);
    max-width: 800px;
    line-height: var(--leading-relaxed);
}

.middle div a {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    padding: var(--space-4) var(--space-8);
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    color: var(--white);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-base);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.middle div a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.middle div a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.middle div a:hover::before {
    left: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .top-texts__info {
        flex-direction: column;
        text-align: center;
        gap: var(--space-6);
    }

    .top-texts__info p {
        margin-right: 0;
        margin-bottom: var(--space-4);
    }

    .service-items {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-8);
    }
}

@media (max-width: 768px) {
    .top {
        height: 80vh;
    }

    .top::before {
        clip-path: polygon(0 0, 85% 0, 70% 100%, 0 100%);
    }

    .top::after {
        clip-path: polygon(0 0, 65% 0, 35% 100%, 0 100%);
    }

    .top-inner {
        padding: 0 var(--space-4);
    }

    .service {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-16);
    }

    .service-items {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        padding: 0 var(--space-4);
    }

    .service-item {
        padding: var(--space-6);
    }

    .middle {
        min-height: 280px;
        margin: var(--space-16) 0;
    }

    .middle div {
        padding: var(--space-6);
    }
}

@media (max-width: 480px) {
    .top {
        height: 70vh;
    }

    .top-btn {
        max-width: 100%;
        width: 250px;
    }

    .service-item__pic img {
        max-width: 80px;
    }

    .middle::after {
        clip-path: polygon(90% 0, 100% 0, 100% 100%, 70% 100%);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .top-title,
    .top-subtitle,
    .top-text,
    .top-btn {
        animation: none;
    }

    .service-item,
    .service-item__pic img,
    .top-btn,
    .middle div a {
        transition: none;
    }
}

/* High Contrast */
@media (prefers-contrast: high) {
    .service-item {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .top-btn,
    .middle div a {
        border: 2px solid var(--white);
    }
}

/* ===== WHY CHOOSE US SECTION ===== */
.us {
    margin-bottom: var(--space-16);
    padding: var(--space-16) 0;
}

.us-inner {
    margin-bottom: var(--space-12);
    text-align: center;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 var(--space-5);
}

.us h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    text-align: center;
    margin-bottom: var(--space-8);
    position: relative;
    padding-bottom: var(--space-4);
}

.us h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.us-inner p {
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-600);
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.us-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.us-item {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: var(--transition-base);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.us-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green), var(--green-600));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.us-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.us-item:hover::before {
    transform: scaleX(1);
}

.us-item div {
    margin-bottom: var(--space-6);
    display: flex;
    justify-content: center;
}

.us-item img {
    width: 80px;
    height: 80px;
    transition: var(--transition-base);
}

.us-item:hover img {
    transform: scale(1.1);
}

.us-item h4 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.us-item p {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
}

/* ===== ABOUT SECTION ===== */
.about {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-32);
    gap: var(--space-16);
    padding: var(--space-16) 0;
}

.about-pics {
    display: flex;
    gap: var(--space-4);
    flex: 1;
    max-width: 600px;
}

.about-pics > div:first-child {
    flex: 2;
}

.about-pics > div:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.about-pics img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-base);
}

.about-pics img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.about-info {
    flex: 1;
    max-width: 795px;
    padding: 0 var(--space-5);
}

.about-info h3 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    position: relative;
    padding-bottom: var(--space-4);
}

.about-info h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.about-info > p {
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
}

.about-info__items {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.about-info__item {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
}

.about-info__item-pic {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--blue-100), var(--blue-200));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-info__item-pic img {
    width: 22px;
    height: 22px;
}

.about-info__item-text span {
    color: var(--blue-800);
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    display: block;
    margin-bottom: var(--space-2);
}

.about-info__item-text p {
    color: var(--gray-600);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-regular);
    margin: 0;
}

/* ===== CONSULTATION SECTION ===== */
.consultation {
    margin-bottom: var(--space-32);
    background-image: url("../images/Consultation.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.consultation::after {
    content: "";
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 88, 168, 0.4));
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.consultation-items {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--space-16) var(--space-5);
    max-width: 1000px;
}

.consultation-items h4 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 5vw, var(--text-6xl));
    color: var(--white);
    text-align: center;
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.consultation-items p {
    font-size: clamp(var(--text-xl), 3vw, var(--text-4xl));
    font-weight: var(--font-regular);
    margin-bottom: var(--space-8);
    color: var(--white);
    text-align: center;
    line-height: var(--leading-relaxed);
}

.consultation-items a {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    padding: var(--space-4) var(--space-12);
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    color: var(--white);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-base);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.consultation-items a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.consultation-items a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
}

.consultation-items a:hover::before {
    left: 0;
}

/* ===== QUESTIONS/FAQ SECTION ===== */
.questions {
    margin-bottom: var(--space-16);
    padding: var(--space-16) 0;
}

.questions h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    text-align: center;
    margin-bottom: var(--space-16);
    position: relative;
    padding-bottom: var(--space-4);
}

.questions h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.questions-accordion {
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.accordion-item {
    background: var(--white);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-4);
    box-shadow: var(--shadow-base);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-base);
}

.accordion-item:hover {
    box-shadow: var(--shadow-md);
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-8);
    cursor: pointer;
    transition: var(--transition-base);
    background: linear-gradient(135deg, var(--gray-50), var(--white));
}

.accordion-header:hover {
    background: linear-gradient(135deg, var(--blue-50), var(--gray-50));
}

.accordion-header h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin: 0;
    line-height: var(--leading-tight);
    flex: 1;
    padding-right: var(--space-4);
}

.accordion-header img {
    width: 24px;
    height: 24px;
    transition: var(--transition-base);
    filter: brightness(0) saturate(100%) invert(25%) sepia(15%) saturate(1000%) hue-rotate(200deg);
}

.accordion-item.active .accordion-header img {
    transform: rotate(180deg);
    filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}

.accordion-content {
    padding: 0 var(--space-8) var(--space-6);
    color: var(--gray-700);
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-item.active .accordion-content {
    max-height: 200px;
    padding: var(--space-4) var(--space-8) var(--space-6);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonios-section {
    padding: var(--space-16) 0;
    text-align: center;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    margin: var(--space-16) 0;
}

.testimonios-section h2 {
    font-weight: var(--font-bold);
    color: var(--blue-800);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    margin-bottom: var(--space-12);
    position: relative;
    padding-bottom: var(--space-4);
}

.testimonios-section h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.testimonios-container {
    width: 90%;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl);
    background: var(--white);
    box-shadow: var(--shadow-lg);
    padding: var(--space-8);
}

.testimonio {
    display: none;
    text-align: center;
    animation: fadeInUp 0.5s ease;
}

.testimonio.active {
    display: block;
}

.testimonio blockquote {
    margin: 0 0 var(--space-6) 0;
    padding: 0;
}

.testimonio blockquote p {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
    font-style: italic;
    position: relative;
    padding: 0 var(--space-4);
}

.testimonio blockquote p::before {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    position: absolute;
    left: -10px;
    top: -10px;
    font-family: serif;
}

.testimonio blockquote p::after {
    content: '"';
    font-size: var(--text-4xl);
    color: var(--primary-blue);
    position: absolute;
    right: -10px;
    bottom: -20px;
    font-family: serif;
}

.testimonio > p {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--blue-800);
    margin-bottom: var(--space-2);
}

.testimonio span {
    font-size: var(--text-base);
    color: var(--gray-600);
    font-style: italic;
}

.testimonios-controles {
    margin-top: var(--space-8);
    display: flex;
    justify-content: center;
    gap: var(--space-5);
}

.testimonios-prev,
.testimonios-next {
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    border: none;
    color: var(--white);
    font-size: var(--text-xl);
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.testimonios-prev:hover,
.testimonios-next:hover {
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== CONTACT SECTION ===== */
.contact {
    display: flex;
}

.contact-inner {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.contact h2 {
    font-weight: var(--font-bold);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--blue-800);
    margin-bottom: var(--space-4);
    position: relative;
    padding-bottom: var(--space-4);
}

.contact h2::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    border-radius: var(--radius-full);
}

.contact-text {
    margin-bottom: var(--space-8);
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-regular);
    color: var(--gray-600);
    max-width: 600px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
}

.contact-label {
    display: block;
    width: 100%;
}

.contact-input,
.contact-label__textarea,
.contact-label__select select {
    width: 100%;
    font-weight: var(--font-regular);
    font-size: var(--text-base);
    padding: var(--space-4) var(--space-6);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    transition: var(--transition-base);
    font-family: var(--font-primary);
}

.contact-input:focus,
.contact-label__textarea:focus,
.contact-label__select select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 88, 168, 0.1);
}

.contact-input::placeholder,
.contact-label__textarea::placeholder {
    color: var(--gray-400);
}

.contact-label__select select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-4) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--space-12);
}

.contact-label__textarea {
    min-height: 120px;
    resize: vertical;
}

.contact-btn {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    width: 100%;
    padding: var(--space-4) var(--space-6);
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-base);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800));
    transition: var(--transition-base);
    z-index: -1;
}

.contact-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-btn:hover:not(:disabled)::before {
    left: 0;
}

.contact-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Form Validation States */
.contact-input.error-input,
.contact-label__textarea.error-input {
    border-color: var(--error);
    background-color: var(--error-light);
}

.contact-input.valid-input,
.contact-label__textarea.valid-input {
    border-color: var(--success);
    background-color: var(--success-light);
}

.contact-mobile__img {
    display: none;
}

/* ===== ADDITIONAL RESPONSIVE DESIGN ===== */

/* Large Tablets */
@media (max-width: 1240px) {
    .about {
        flex-direction: column;
        gap: var(--space-12);
        text-align: center;
    }

    .about-pics {
        max-width: 100%;
        justify-content: center;
    }

    .about-info {
        max-width: 100%;
        padding: 0 var(--space-4);
    }

    .about-info h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .us-items {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-6);
    }
}

/* Tablets */
@media (max-width: 768px) {
    .us {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-12);
    }

    .us-items {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        padding: 0 var(--space-4);
    }

    .us-item {
        padding: var(--space-6);
    }

    .about {
        margin-bottom: var(--space-20);
        padding: var(--space-12) 0;
    }

    .about-pics {
        flex-direction: column;
        gap: var(--space-4);
    }

    .about-pics > div:last-child {
        flex-direction: row;
        gap: var(--space-3);
    }

    .consultation {
        min-height: 400px;
        margin-bottom: var(--space-20);
    }

    .consultation-items {
        padding: var(--space-12) var(--space-4);
    }

    .questions {
        padding: var(--space-12) 0;
        margin-bottom: var(--space-12);
    }

    .questions-accordion {
        padding: 0 var(--space-4);
    }

    .accordion-header {
        padding: var(--space-4) var(--space-5);
    }

    .accordion-header h3 {
        font-size: var(--text-base);
    }

    .accordion-content {
        padding: 0 var(--space-5) var(--space-4);
    }

    .accordion-item.active .accordion-content {
        padding: var(--space-3) var(--space-5) var(--space-4);
    }

    .testimonios-section {
        padding: var(--space-12) 0;
        margin: var(--space-12) 0;
    }

    .testimonios-container {
        width: 95%;
        padding: var(--space-6);
    }

    .testimonio blockquote p {
        font-size: var(--text-base);
        padding: 0 var(--space-2);
    }

    .testimonios-controles {
        gap: var(--space-4);
    }

    .testimonios-prev,
    .testimonios-next {
        width: 45px;
        height: 45px;
        font-size: var(--text-lg);
    }

    .contact {
        padding: var(--space-20) 0;
        margin-bottom: var(--space-16);
    }

    .contact-inner {
        padding: 0 var(--space-4);
    }

    .contact h2::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .contact-text {
        text-align: center;
        margin: 0 auto var(--space-8);
    }
}

/* Mobile */
@media (max-width: 480px) {
    .us-item {
        padding: var(--space-4);
    }

    .us-item img {
        width: 60px;
        height: 60px;
    }

    .about-pics > div:last-child {
        flex-direction: column;
    }

    .about-info__item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .about-info__item-pic {
        align-self: center;
    }

    .consultation {
        min-height: 350px;
    }

    .consultation-items {
        padding: var(--space-8) var(--space-3);
    }

    .consultation-items a {
        padding: var(--space-3) var(--space-8);
        font-size: var(--text-lg);
    }

    .accordion-header {
        padding: var(--space-3) var(--space-4);
    }

    .accordion-header h3 {
        font-size: var(--text-sm);
    }

    .accordion-header img {
        width: 20px;
        height: 20px;
    }

    .testimonios-container {
        padding: var(--space-4);
    }

    .testimonio blockquote p::before,
    .testimonio blockquote p::after {
        font-size: var(--text-2xl);
    }

    .testimonios-prev,
    .testimonios-next {
        width: 40px;
        height: 40px;
        font-size: var(--text-base);
    }

    .contact-input,
    .contact-label__textarea,
    .contact-label__select select {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-base);
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    .us-item,
    .about-pics img,
    .consultation-items a,
    .accordion-item,
    .testimonios-prev,
    .testimonios-next,
    .contact-btn {
        transition: none;
    }

    .testimonio {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .us-item,
    .accordion-item,
    .testimonios-container {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .consultation-items a,
    .testimonios-prev,
    .testimonios-next,
    .contact-btn {
        border: 2px solid var(--white);
    }

    .contact-input,
    .contact-label__textarea,
    .contact-label__select select {
        border-width: 2px;
        border-color: var(--gray-900);
    }

    .contact-input:focus,
    .contact-label__textarea:focus,
    .contact-label__select select:focus {
        border-color: var(--primary-blue);
        box-shadow: none;
    }
}

/* Focus Styles for Accessibility */
.us-item:focus,
.accordion-header:focus,
.testimonios-prev:focus,
.testimonios-next:focus,
.consultation-items a:focus,
.contact-input:focus,
.contact-label__textarea:focus,
.contact-label__select select:focus,
.contact-btn:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}
