/* ===== FOOTER STYLES ===== */

/* Main Footer */
.footer {
    background: linear-gradient(135deg, var(--blue-900) 0%, var(--blue-800) 100%);
    padding-top: var(--space-20);
    margin-top: var(--space-24);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
}

/* Footer Content */
.footer-inner {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: var(--space-12);
    gap: var(--space-12);
}

/* Footer Info Section */
.footer-info {
    flex: 1;
    max-width: 500px;
}

.footer-logo {
    margin-bottom: var(--space-5);
    display: inline-block;
    transition: var(--transition-base);
}

.footer-logo:hover {
    transform: scale(1.02);
}

.footer-logo img {
    width: 190px;
    height: auto;
}

.footer-text {
    max-width: 494px;
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--white);
    margin-bottom: var(--space-12);
    font-family: var(--font-secondary);
}

/* Footer Navigation */
.footer-items {
    display: flex;
    max-width: 995px;
    width: 100%;
    justify-content: space-between;
    gap: var(--space-8);
}

.footer-item {
    flex: 1;
}

.footer-item h5,
.footer-item__title {
    margin-bottom: var(--space-8);
    color: var(--white);
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    position: relative;
    padding-bottom: var(--space-3);
}

.footer-item h5::after,
.footer-item__title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-green), var(--green-400));
    border-radius: var(--radius-full);
}

.footer-item li,
.footer-item__list {
    margin-bottom: var(--space-6);
}

.footer-item li:last-child,
.footer-item__list:last-child {
    margin-bottom: 0;
}

.footer-item a,
.footer-item__link {
    font-weight: var(--font-light);
    font-size: var(--text-base);
    color: var(--gray-200);
    display: block;
    transition: var(--transition-base);
    padding: var(--space-1) 0;
    position: relative;
}

.footer-item a:hover,
.footer-item__link:hover {
    color: var(--primary-green);
    transform: translateX(5px);
}

/* Footer Contact Section */
.footer-contact {
    display: flex;
    flex-direction: column;
}

.footer-contact__title {
    margin-bottom: var(--space-8);
    color: var(--white);
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    position: relative;
    padding-bottom: var(--space-3);
}

.footer-contact__title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-green), var(--green-400));
    border-radius: var(--radius-full);
}

.footer-contact a,
.footer-contact__link {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-8);
    color: var(--gray-200);
    transition: var(--transition-base);
    padding: var(--space-2);
    border-radius: var(--radius-md);
}

.footer-contact a:hover,
.footer-contact__link:hover {
    color: var(--primary-green);
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.footer-contact a img,
.footer-contact__link img {
    width: 20px;
    height: 20px;
    margin-right: var(--space-5);
    filter: brightness(0) invert(1);
    transition: var(--transition-base);
}

.footer-contact a:hover img,
.footer-contact__link:hover img {
    filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}

.footer-contact a span,
.footer-contact__link span {
    font-size: var(--text-base);
    font-weight: var(--font-light);
}

/* Footer Bottom */
.footer-bottom {
    padding: var(--space-8) var(--space-5);
    margin: 0 auto;
}

.footer-bottom__inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.footer-bottom__text {
    font-weight: var(--font-regular);
    font-size: var(--text-base);
    color: var(--white);
    text-align: center;
    font-family: var(--font-secondary);
}

/* Social Media */
.social-share {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.social-share__title {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--white);
    margin-right: var(--space-2);
}

.social-share__links {
    display: flex;
    gap: var(--space-3);
}

.social-share__links a {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--blue-800), var(--blue-700));
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.social-share__links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    opacity: 0;
    transition: var(--transition-base);
}

.social-share__links a:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.social-share__links a:hover::before {
    opacity: 1;
}

.social-share__links a svg {
    width: 20px;
    height: 20px;
    fill: var(--white);
    position: relative;
    z-index: 1;
    transition: var(--transition-base);
}

.social-share__links a:hover svg {
    transform: scale(1.1);
}

/* WhatsApp Float (if exists) */
.footer-info__wp {
    position: fixed;
    bottom: var(--space-4);
    right: var(--space-4);
    display: flex;
    align-items: center;
    z-index: var(--z-fixed);
    background: linear-gradient(135deg, #25D366, #128C7E);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-base);
}

.footer-info__wp:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.footer-info__wp img {
    width: 32px;
    height: 32px;
    margin-right: var(--space-2);
}

.footer-info__wp span {
    font-weight: var(--font-bold);
    font-size: var(--text-sm);
    color: var(--white);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Tablets */
@media (max-width: 1240px) {
    .footer-inner {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--space-16);
    }

    .footer-items {
        flex-direction: column;
        align-items: center;
        gap: var(--space-12);
        max-width: 600px;
    }

    .footer-text {
        margin-bottom: var(--space-16);
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        max-width: 495px;
        position: relative;
        padding-bottom: var(--space-16);
    }

    .footer-text::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-green), var(--green-400));
    }

    .footer-contact__title::after,
    .footer-item h5::after,
    .footer-item__title::after {
        width: 60px;
    }

    .footer-item {
        margin-bottom: var(--space-8);
    }

    .footer-logo {
        margin-bottom: var(--space-10);
    }
}

/* Tablets */
@media (max-width: 768px) {
    .footer {
        padding-top: var(--space-16);
        margin-top: var(--space-16);
    }

    .footer-bottom__inner {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .social-share {
        flex-direction: column;
        gap: var(--space-3);
    }

    .social-share__title {
        margin-bottom: var(--space-2);
        margin-right: 0;
    }

    .footer-text {
        font-size: var(--text-sm);
        line-height: var(--leading-relaxed);
        font-weight: var(--font-light);
    }

    .footer-item h5,
    .footer-item__title {
        font-size: var(--text-lg);
    }

    .footer-item a,
    .footer-item__link {
        font-size: var(--text-sm);
    }

    .footer-contact a,
    .footer-contact__link {
        justify-content: center;
    }

    .footer-info__wp {
        bottom: var(--space-3);
        right: var(--space-3);
        padding: var(--space-2) var(--space-3);
    }

    .footer-info__wp img {
        width: 28px;
        height: 28px;
    }

    .footer-info__wp span {
        font-size: var(--text-xs);
    }
}

/* Mobile */
@media (max-width: 480px) {
    .footer-inner {
        gap: var(--space-12);
    }

    .footer-items {
        gap: var(--space-8);
    }

    .footer-logo img {
        width: 150px;
    }

    .social-share__links a {
        width: 36px;
        height: 36px;
    }

    .social-share__links a svg {
        width: 18px;
        height: 18px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .footer-logo,
    .footer-item a,
    .footer-contact a,
    .social-share__links a,
    .footer-info__wp {
        transition: none;
    }
}

/* High Contrast */
@media (prefers-contrast: high) {
    .footer {
        border-top: 3px solid var(--white);
    }

    .footer-item a:hover,
    .footer-contact a:hover {
        background-color: var(--white);
        color: var(--black);
    }

    .social-share__links a {
        border: 2px solid var(--white);
    }
}

/* Focus Styles */
.footer-item a:focus,
.footer-contact a:focus,
.social-share__links a:focus,
.footer-logo:focus {
    outline: 2px solid var(--primary-green);
    outline-offset: 2px;
}
