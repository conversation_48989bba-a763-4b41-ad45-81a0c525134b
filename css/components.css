/* ===== REUSABLE COMPONENTS ===== */

/* ===== BUTTONS ===== */

/* Base Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-weight: var(--font-semibold);
    font-size: var(--text-base);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    min-height: 48px;
    box-shadow: var(--shadow-sm);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    border: 2px solid transparent;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--blue-700), var(--blue-800));
    transition: var(--transition-base);
    z-index: -1;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover:not(:disabled)::before {
    left: 0;
}

/* Secondary Button */
.btn-secondary {
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    color: var(--white);
    border: 2px solid transparent;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--green-700), var(--green-800));
    transition: var(--transition-base);
    z-index: -1;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary:hover:not(:disabled)::before {
    left: 0;
}

/* Outline Button */
.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--blue-50);
    color: var(--blue-700);
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-5) var(--space-8);
    font-size: var(--text-lg);
    min-height: 56px;
}

.btn-xl {
    padding: var(--space-6) var(--space-10);
    font-size: var(--text-xl);
    min-height: 64px;
}

/* Button with Icon */
.btn-icon {
    gap: var(--space-2);
}

.btn-icon svg {
    width: 20px;
    height: 20px;
    transition: var(--transition-base);
}

.btn-icon:hover svg {
    transform: translateX(2px);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: var(--space-8);
    right: var(--space-8);
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    color: var(--white);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-lg);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.back-to-top svg {
    width: 24px;
    height: 24px;
    transition: var(--transition-base);
}

.back-to-top:hover svg {
    transform: translateY(-2px);
}

/* ===== CARDS ===== */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-base);
    padding: var(--space-6);
    transition: var(--transition-base);
    border: 1px solid var(--gray-200);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    margin-bottom: var(--space-4);
}

.card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.card-subtitle {
    font-size: var(--text-base);
    color: var(--gray-600);
    margin-bottom: var(--space-4);
}

.card-body {
    margin-bottom: var(--space-4);
}

.card-text {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
}

.card-footer {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--space-4);
    margin-top: var(--space-4);
}

/* Service Card Variant */
.service-card {
    text-align: center;
    padding: var(--space-8);
    max-width: 400px;
    margin: 0 auto;
}

.service-card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--blue-50), var(--blue-100));
    border-radius: var(--radius-2xl);
    transition: var(--transition-base);
}

.service-card:hover .service-card-icon {
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    transform: scale(1.1);
}

.service-card:hover .service-card-icon img {
    filter: brightness(0) invert(1);
}

.service-card-icon img {
    width: 40px;
    height: 40px;
    transition: var(--transition-base);
}

/* ===== FORM ELEMENTS ===== */

.form-container {
    display: flex;
    flex-direction: column;
}

/* Input Groups */
.input-group {
    position: relative;
    margin-bottom: var(--space-5);
    display: flex;
    flex-direction: column;
}

.input-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.input-label.required::after {
    content: ' *';
    color: var(--error);
}

/* Input Fields */
.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    font-family: var(--font-primary);
    transition: var(--transition-base);
    background: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 88, 168, 0.1);
}

.form-input:invalid {
    border-color: var(--error);
}

.form-input::placeholder {
    color: var(--gray-400);
}

/* Textarea */
.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* Select */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-3) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--space-10);
}

/* Error Messages */
.error-message {
    color: var(--error);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.error-message::before {
    content: '⚠';
    font-size: var(--text-base);
}

/* Success Messages */
.success-message {
    color: var(--success);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.success-message::before {
    content: '✓';
    font-size: var(--text-base);
}

/* ===== LOADING SPINNER ===== */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-blue);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--blue-100);
    color: var(--blue-800);
}

.badge-success {
    background: var(--green-100);
    color: var(--green-800);
}

.badge-warning {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.badge-error {
    background: var(--error-light);
    color: var(--error-dark);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .back-to-top {
        bottom: var(--space-5);
        right: var(--space-5);
        width: 45px;
        height: 45px;
    }

    .back-to-top svg {
        width: 20px;
        height: 20px;
    }

    .service-card {
        padding: var(--space-6);
    }

    .service-card-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--space-4);
    }

    .service-card-icon img {
        width: 30px;
        height: 30px;
    }

    .btn-lg {
        padding: var(--space-4) var(--space-6);
        font-size: var(--text-base);
    }

    .btn-xl {
        padding: var(--space-5) var(--space-8);
        font-size: var(--text-lg);
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    .btn,
    .card,
    .back-to-top,
    .service-card-icon,
    .form-input {
        transition: none;
    }

    .spinner,
    .btn-loading::after {
        animation: none;
    }
}

/* Focus Styles */
.btn:focus,
.form-input:focus,
.form-select:focus,
.back-to-top:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }

    .form-input,
    .form-select {
        border-width: 2px;
    }

    .card {
        border-width: 2px;
    }
}
