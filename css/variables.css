*,
*::after,
*::before {
    box-sizing: border-box;
}

:root {
    /* ===== BRAND COLORS ===== */
    --primary-blue: #0058A8;
    --primary-green: #009D5A;

    /* ===== EXTENDED COLOR PALETTE ===== */
    /* Blue Variations */
    --blue-50: #E6F2FF;
    --blue-100: #CCE5FF;
    --blue-200: #99CCFF;
    --blue-300: #66B2FF;
    --blue-400: #3399FF;
    --blue-500: #0058A8; /* Primary Blue */
    --blue-600: #004A8F;
    --blue-700: #003C76;
    --blue-800: #002E5D;
    --blue-900: #002044;

    /* Green Variations */
    --green-50: #E6F7F0;
    --green-100: #CCEFE1;
    --green-200: #99DFC3;
    --green-300: #66CFA5;
    --green-400: #33BF87;
    --green-500: #009D5A; /* Primary Green */
    --green-600: #00844B;
    --green-700: #006B3C;
    --green-800: #00522D;
    --green-900: #00391E;

    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    --black: #000319;

    /* Semantic Colors */
    --success: var(--green-500);
    --success-light: var(--green-100);
    --success-dark: var(--green-700);
    --warning: #F59E0B;
    --warning-light: #FEF3C7;
    --warning-dark: #D97706;
    --error: #EF4444;
    --error-light: #FEE2E2;
    --error-dark: #DC2626;
    --info: var(--blue-500);
    --info-light: var(--blue-100);
    --info-dark: var(--blue-700);

    /* Legacy Support */
    --primary-white: var(--white);
    --primary-black: var(--black);

    /* ===== TYPOGRAPHY SCALE ===== */
    /* Font Families */
    --font-primary: 'Outfit', sans-serif;
    --font-secondary: 'Open Sans', sans-serif;
    --font-accent: 'Poppins', sans-serif;

    /* Font Sizes - Mobile First */
    --text-xs: 0.75rem;    /* 12px */
    --text-sm: 0.875rem;   /* 14px */
    --text-base: 1rem;     /* 16px */
    --text-lg: 1.125rem;   /* 18px */
    --text-xl: 1.25rem;    /* 20px */
    --text-2xl: 1.5rem;    /* 24px */
    --text-3xl: 1.875rem;  /* 30px */
    --text-4xl: 2.25rem;   /* 36px */
    --text-5xl: 3rem;      /* 48px */
    --text-6xl: 3.75rem;   /* 60px */

    /* Font Weights */
    --font-light: 300;
    --font-regular: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Legacy Font Sizes */
    --font-size-xs: var(--text-xs);
    --font-size-s: var(--text-sm);
    --font-size-m: var(--text-base);
    --font-size-l: var(--text-xl);
    --font-size-xl: var(--text-2xl);

    /* Legacy Font Weights */
    --font-weight-light: var(--font-light);
    --font-weight-regular: var(--font-regular);
    --font-weight-medium: var(--font-medium);
    --font-weight-semibold: var(--font-semibold);
    --font-weight-bold: var(--font-bold);

    /* ===== SPACING SYSTEM ===== */
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */
    --space-32: 8rem;     /* 128px */

    /* ===== BORDER RADIUS ===== */
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-full: 9999px;

    /* ===== SHADOWS ===== */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* ===== TRANSITIONS ===== */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;

    /* ===== BREAKPOINTS (for reference) ===== */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    /* ===== CONTAINER SIZES ===== */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1680px;

    /* ===== Z-INDEX SCALE ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

html {
    scroll-behavior: smooth;
}