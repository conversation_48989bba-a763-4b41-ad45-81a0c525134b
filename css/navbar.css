/* ===== NAVBAR STYLES ===== */

/* Header Container */
.header {
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
    padding: var(--space-5) 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header.scrolled {
    padding: var(--space-3) 0;
    box-shadow: var(--shadow-lg);
}

.header-items {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo */
.header-logo {
    display: flex;
    align-items: center;
    transition: var(--transition-base);
}

.header-logo img {
    width: 140px;
    height: auto;
    transition: var(--transition-base);
}

.header-logo:hover {
    transform: scale(1.02);
}

/* Mobile Menu Button */
.header-menuImg {
    display: none;
    cursor: pointer;
    width: 24px;
    height: 24px;
    transition: var(--transition-base);
    z-index: calc(var(--z-sticky) + 1);
}

.header-menuImg:hover {
    transform: scale(1.1);
}

/* Navigation */
.header-nav {
    display: flex;
    align-items: center;
    transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
}

.header-nav__list {
    margin-right: var(--space-10);
    position: relative;
}

.header-nav__list:last-child {
    margin-right: 0;
}

/* Navigation Links */
.header-nav__link {
    font-size: var(--text-xl);
    font-weight: var(--font-regular);
    color: var(--blue-800);
    transition: var(--transition-base);
    display: inline-block;
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
}

.header-nav__link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
    transition: var(--transition-base);
    transform: translateX(-50%);
}

.header-nav__link:hover {
    color: var(--primary-blue);
    background-color: var(--blue-50);
    transform: translateY(-2px);
}

.header-nav__link:hover::after {
    width: 80%;
}

.header-nav__link.active {
    color: var(--primary-blue);
    background-color: var(--blue-100);
}

.header-nav__link.active::after {
    width: 80%;
}

/* CTA Button */
.header-btn {
    font-size: var(--text-xl);
    font-weight: var(--font-extrabold);
    color: var(--white);
    padding: var(--space-4) var(--space-8);
    background: linear-gradient(135deg, var(--primary-blue), var(--blue-600));
    border: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-base);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.header-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-green), var(--green-600));
    transition: var(--transition-base);
    z-index: -1;
}

.header-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.header-btn:hover::before {
    left: 0;
}

.header-btn:active {
    transform: translateY(-1px);
}

/* Mobile Menu States */
.menu-open .header-menuImg {
    transform: rotate(90deg);
}

.stop-scroll {
    overflow: hidden;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop */
@media (max-width: 1440px) {
    .header-nav__list:not(:last-child) {
        margin-right: var(--space-4);
    }
}

/* Medium Desktop */
@media (max-width: 1320px) {
    .header-nav__link {
        font-size: var(--text-base);
    }

    .header-btn {
        font-size: var(--text-base);
        padding: var(--space-3) var(--space-5);
    }

    .header-logo img {
        width: 110px;
    }
}

/* Tablet and Mobile */
@media (max-width: 1024px) {
    .header-nav {
        position: fixed;
        top: 0;
        left: -100%;
        z-index: var(--z-modal);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
        width: 100%;
        height: 100vh;
        transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }

    .header-menuImg {
        display: block;
        position: relative;
        z-index: calc(var(--z-modal) + 1);
    }

    .header-nav__active {
        left: 0;
    }

    .header-nav__list {
        margin: 0 0 var(--space-10) 0;
        opacity: 0;
        transform: translateY(20px);
        animation: slideInUp 0.3s ease forwards;
    }

    .header-nav__list:nth-child(1) { animation-delay: 0.1s; }
    .header-nav__list:nth-child(2) { animation-delay: 0.2s; }
    .header-nav__list:nth-child(3) { animation-delay: 0.3s; }
    .header-nav__list:nth-child(4) { animation-delay: 0.4s; }
    .header-nav__list:nth-child(5) { animation-delay: 0.5s; }
    .header-nav__list:nth-child(6) { animation-delay: 0.6s; }
    .header-nav__list:nth-child(7) { animation-delay: 0.7s; }

    .header-nav__list:not(:last-child) {
        margin-right: 0;
    }

    .header-nav__link {
        font-size: var(--text-2xl);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-xl);
        text-align: center;
        min-width: 200px;
    }

    .header-btn {
        font-size: var(--text-xl);
        padding: var(--space-4) var(--space-8);
        border-radius: var(--radius-xl);
        margin-top: var(--space-4);
    }
}

/* Mobile Phones */
@media (max-width: 768px) {
    .header {
        padding: var(--space-4) 0;
    }

    .header-logo img {
        width: 100px;
    }

    .header-nav__link {
        font-size: var(--text-xl);
        min-width: 180px;
    }

    .header-btn {
        font-size: var(--text-lg);
        padding: var(--space-3) var(--space-6);
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .header-logo img {
        width: 90px;
    }

    .header-nav__link {
        font-size: var(--text-lg);
        min-width: 160px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .header-nav,
    .header-nav__link,
    .header-btn,
    .header-logo,
    .header-menuImg {
        transition: none;
        animation: none;
    }
    
    .header-nav__list {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .header {
        border-bottom: 2px solid var(--black);
    }
    
    .header-nav__link {
        border: 1px solid transparent;
    }
    
    .header-nav__link:hover,
    .header-nav__link:focus {
        border-color: var(--black);
        background-color: var(--white);
    }
    
    .header-btn {
        border: 2px solid var(--black);
    }
}

/* Focus Styles for Accessibility */
.header-nav__link:focus,
.header-btn:focus,
.header-logo:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

.header-menuImg:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 4px;
    border-radius: var(--radius-sm);
}
